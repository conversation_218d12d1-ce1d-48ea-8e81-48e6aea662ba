package services

import (
	"context"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func TestUserService_ValidateUserServiceAccess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := zaptest.NewLogger(t)
	userID := appioid.MustParse("usr_00000000000000000000000001")
	serviceID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - user has access to service", func(t *testing.T) {
		mockRepo := mocks.NewMockUserRepositoryInterface(ctrl)
		service := NewUserService(mockRepo, logger)

		mockRepo.EXPECT().
			ValidateUserServiceAccess(gomock.Any(), userID, serviceID).
			Return(true, nil).
			Times(1)

		err := service.ValidateUserServiceAccess(context.Background(), userID, serviceID)

		assert.NoError(t, err)
	})

	t.Run("Error - user does not have access to service", func(t *testing.T) {
		mockRepo := mocks.NewMockUserRepositoryInterface(ctrl)
		service := NewUserService(mockRepo, logger)

		mockRepo.EXPECT().
			ValidateUserServiceAccess(gomock.Any(), userID, serviceID).
			Return(false, nil).
			Times(1)

		err := service.ValidateUserServiceAccess(context.Background(), userID, serviceID)

		assert.Error(t, err)
		assert.Equal(t, pkg.ErrForbidden, err)
	})

	t.Run("Error - repository error", func(t *testing.T) {
		mockRepo := mocks.NewMockUserRepositoryInterface(ctrl)
		service := NewUserService(mockRepo, logger)

		mockRepo.EXPECT().
			ValidateUserServiceAccess(gomock.Any(), userID, serviceID).
			Return(false, assert.AnError).
			Times(1)

		err := service.ValidateUserServiceAccess(context.Background(), userID, serviceID)

		assert.Error(t, err)
		assert.Equal(t, pkg.ErrInternal, err)
	})

	t.Run("Error - nil user ID", func(t *testing.T) {
		mockRepo := mocks.NewMockUserRepositoryInterface(ctrl)
		service := NewUserService(mockRepo, logger)

		err := service.ValidateUserServiceAccess(context.Background(), nil, serviceID)

		assert.Error(t, err)
		assert.Equal(t, pkg.ErrInvalidInput, err)
	})

	t.Run("Error - nil service ID", func(t *testing.T) {
		mockRepo := mocks.NewMockUserRepositoryInterface(ctrl)
		service := NewUserService(mockRepo, logger)

		err := service.ValidateUserServiceAccess(context.Background(), userID, nil)

		assert.Error(t, err)
		assert.Equal(t, pkg.ErrInvalidInput, err)
	})

	t.Run("Error - both IDs nil", func(t *testing.T) {
		mockRepo := mocks.NewMockUserRepositoryInterface(ctrl)
		service := NewUserService(mockRepo, logger)

		err := service.ValidateUserServiceAccess(context.Background(), nil, nil)

		assert.Error(t, err)
		assert.Equal(t, pkg.ErrInvalidInput, err)
	})

	t.Run("Success - different user and service", func(t *testing.T) {
		mockRepo := mocks.NewMockUserRepositoryInterface(ctrl)
		service := NewUserService(mockRepo, logger)

		differentUserID := appioid.MustParse("usr_00000000000000000000000002")
		differentServiceID := appioid.MustParse("svc_00000000000000000000000002")

		mockRepo.EXPECT().
			ValidateUserServiceAccess(gomock.Any(), differentUserID, differentServiceID).
			Return(true, nil).
			Times(1)

		err := service.ValidateUserServiceAccess(context.Background(), differentUserID, differentServiceID)

		assert.NoError(t, err)
	})
}
