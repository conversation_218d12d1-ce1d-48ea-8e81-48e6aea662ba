package services

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"api.appio.so/pkg/roles"

	"api.appio.so/pkg"
	"api.appio.so/pkg/config"
	"api.appio.so/repositories"
	"github.com/appio-so/go-appioid"
	"go.uber.org/zap"
)

type APIKeyService struct {
	repository repositories.APIKeyRepositoryInterface
	authKeys   *config.AuthConfig
	logger     *zap.Logger
	attempts   map[string]*APIKeyAttempt
	attemptsMu sync.RWMutex
	config     APIKeyServiceConfig
}

// APIKeyServiceConfig holds configurable parameters for the API key service
type APIKeyServiceConfig struct {
	MaxFailures      int           // Number of failures before blocking
	MaxBlockDuration time.Duration // Maximum block duration to prevent overflow
	MinKeyLength     int           // Minimum API key length
	MaxKeyLength     int           // Maximum API key length
	CleanupInterval  time.Duration // How often to clean up old attempts
	AttemptRetention time.Duration // How long to keep attempt records
}

type APIKeyAttempt struct {
	Attempts  int
	LastTry   time.Time
	BlockedAt *time.Time
}

// DefaultAPIKeyServiceConfig returns sensible defaults for the API key service
func DefaultAPIKeyServiceConfig() APIKeyServiceConfig {
	return APIKeyServiceConfig{
		MaxFailures:      5,
		MaxBlockDuration: 24 * time.Hour, // Cap at 24 hours to prevent overflow
		MinKeyLength:     50,
		MaxKeyLength:     200, // Allow longer keys for demo API keys
		CleanupInterval:  10 * time.Minute,
		AttemptRetention: 24 * time.Hour,
	}
}

func NewAPIKeyService(repository repositories.APIKeyRepositoryInterface, authKeys *config.AuthConfig, logger *zap.Logger) *APIKeyService {
	return NewAPIKeyServiceWithConfig(repository, authKeys, logger, DefaultAPIKeyServiceConfig())
}

func NewAPIKeyServiceWithConfig(repository repositories.APIKeyRepositoryInterface, authKeys *config.AuthConfig, logger *zap.Logger, config APIKeyServiceConfig) *APIKeyService {
	service := &APIKeyService{
		repository: repository,
		authKeys:   authKeys,
		logger:     logger,
		attempts:   make(map[string]*APIKeyAttempt),
		config:     config,
	}

	go func() {
		ticker := time.NewTicker(config.CleanupInterval)
		defer ticker.Stop()

		for range ticker.C {
			service.cleanupOldAttempts()
		}
	}()

	return service
}

// Quick and light validation
func (s *APIKeyService) IsValidApiKey(apiKey string) bool {
	return len(apiKey) >= s.config.MinKeyLength && len(apiKey) <= s.config.MaxKeyLength
}

func (s *APIKeyService) GetByAPIKey(ctx context.Context, apiKey string) (usrID, svcID *appioid.ID, role roles.Role, err error) {
	if !s.IsValidApiKey(apiKey) {
		return nil, nil, roles.Unknown, pkg.ErrInvalidInput
	}

	if s.isBlocked(apiKey) {
		return nil, nil, roles.Unknown, pkg.ErrTooManyAttempts
	}
	s.recordAttempt(apiKey)

	role, err = s.getRoleByAPIKey(ctx, apiKey)
	if err != nil {
		return nil, nil, roles.Unknown, pkg.ErrUnauthorized
	}

	// reset on success authentication
	s.resetAttempt(apiKey)

	if role == roles.Api || role == roles.Dashboard {
		userID, usrID, err := s.repository.FindBy(ctx, apiKey)
		if err != nil {
			return nil, nil, roles.Unknown, pkg.ErrUnauthorized
		}
		return userID, usrID, role, err
	}

	return nil, nil, role, err
}

// --------------------------------------------------------------------------------------------------------------------

func (s *APIKeyService) getRoleByAPIKey(ctx context.Context, apiKey string) (roles.Role, error) {
	// internal: app.appio.so
	if s.isAppKey(apiKey) {
		return roles.AppAppioSo, nil
	}

	// internal: ios app
	if s.isIOSKey(apiKey) {
		return roles.IOS, nil
	}

	// internal: android app
	if s.isAndroidKey(apiKey) {
		return roles.Android, nil
	}

	// internal: demo.appio.so
	if s.isDemoKey(apiKey) {
		return roles.DemoAppioSo, nil
	}

	// public: demo.appio.so curl API
	if ok, _, err := s.parseDemoAPIKey(apiKey); ok {
		return roles.ApiDemo, err
	}

	// public: API general use
	active := s.repository.IsActiveAPIKey(ctx, apiKey)
	if !active {
		return roles.Unknown, nil
	}

	return roles.Api, nil
}

// Special handling for demo keys. ServiceID is the last 31 (`_demo` + 26 for ID) characters of the key
func (s *APIKeyService) parseDemoAPIKey(apiKey string) (ok bool, svcID *appioid.ID, err error) {
	if !strings.HasPrefix(apiKey, "demo_") || len(apiKey) < 31 {
		return false, nil, nil
	}

	svcID, err = appioid.Parse(fmt.Sprintf("demo_svc_%s", apiKey[len(apiKey)-26:]))
	if err != nil {
		s.logger.Warn("parsing demo key", zap.String("key", apiKey), zap.Error(err))
		return true, nil, pkg.ErrInvalidInput
	}

	return true, svcID, nil
}

func (s *APIKeyService) isAppKey(apiKey string) bool {
	return apiKey == s.authKeys.App
}

func (s *APIKeyService) isIOSKey(apiKey string) bool {
	return apiKey == s.authKeys.IOS
}

func (s *APIKeyService) isAndroidKey(apiKey string) bool {
	return apiKey == s.authKeys.Android
}

func (s *APIKeyService) isDemoKey(apiKey string) bool {
	return apiKey == s.authKeys.Demo
}

func (s *APIKeyService) isBlocked(apiKey string) bool {
	s.attemptsMu.RLock()
	attempt, exists := s.attempts[apiKey]
	if !exists {
		s.attemptsMu.RUnlock()
		return false
	}

	blocked := false
	if attempt.BlockedAt != nil {
		// Simple fixed block duration - much simpler and more predictable
		blocked = time.Since(*attempt.BlockedAt) < s.config.MaxBlockDuration
	}
	s.attemptsMu.RUnlock()

	return blocked
}

func (s *APIKeyService) recordAttempt(apiKey string) {
	s.attemptsMu.Lock()
	defer s.attemptsMu.Unlock()

	attempt, exists := s.attempts[apiKey]
	if !exists {
		attempt = &APIKeyAttempt{}
		s.attempts[apiKey] = attempt
	}

	attempt.Attempts++
	attempt.LastTry = time.Now()

	if attempt.Attempts >= s.config.MaxFailures { // Block after configured failures
		now := time.Now()
		attempt.BlockedAt = &now
	}
}

// Reset on success
func (s *APIKeyService) resetAttempt(apiKey string) {
	delete(s.attempts, apiKey)
}

func (s *APIKeyService) cleanupOldAttempts() {
	s.attemptsMu.Lock()
	defer s.attemptsMu.Unlock()

	now := time.Now()
	for key, attempt := range s.attempts {
		// Remove entries older than configured retention period
		if now.Sub(attempt.LastTry) > s.config.AttemptRetention {
			delete(s.attempts, key)
		}
	}
}
