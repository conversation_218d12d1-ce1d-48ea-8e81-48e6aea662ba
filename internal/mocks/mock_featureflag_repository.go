// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/featureflag_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/featureflag_repository.go -destination=internal/mocks/mock_featureflag_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	gomock "go.uber.org/mock/gomock"
)

// MockFeatureFlagRepositoryInterface is a mock of FeatureFlagRepositoryInterface interface.
type MockFeatureFlagRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockFeatureFlagRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockFeatureFlagRepositoryInterfaceMockRecorder is the mock recorder for MockFeatureFlagRepositoryInterface.
type MockFeatureFlagRepositoryInterfaceMockRecorder struct {
	mock *MockFeatureFlagRepositoryInterface
}

// NewMockFeatureFlagRepositoryInterface creates a new mock instance.
func NewMockFeatureFlagRepositoryInterface(ctrl *gomock.Controller) *MockFeatureFlagRepositoryInterface {
	mock := &MockFeatureFlagRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockFeatureFlagRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFeatureFlagRepositoryInterface) EXPECT() *MockFeatureFlagRepositoryInterfaceMockRecorder {
	return m.recorder
}

// GetBy mocks base method.
func (m *MockFeatureFlagRepositoryInterface) FindBy(ctx context.Context, platform models.Platform, version string) (*models.FeatureFlag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindBy", ctx, platform, version)
	ret0, _ := ret[0].(*models.FeatureFlag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBy indicates an expected call of GetBy.
func (mr *MockFeatureFlagRepositoryInterfaceMockRecorder) GetBy(ctx, platform, version any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindBy", reflect.TypeOf((*MockFeatureFlagRepositoryInterface)(nil).FindBy), ctx, platform, version)
}
