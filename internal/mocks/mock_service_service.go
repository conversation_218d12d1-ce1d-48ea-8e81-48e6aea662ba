// Code generated by MockGen. DO NOT EDIT.
// Source: services/service_service.go
//
// Generated by this command:
//
//	mockgen -source=services/service_service.go -destination=internal/mocks/mock_service_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockServiceServiceInterface is a mock of ServiceServiceInterface interface.
type MockServiceServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockServiceServiceInterfaceMockRecorder is the mock recorder for MockServiceServiceInterface.
type MockServiceServiceInterfaceMockRecorder struct {
	mock *MockServiceServiceInterface
}

// NewMockServiceServiceInterface creates a new mock instance.
func NewMockServiceServiceInterface(ctrl *gomock.Controller) *MockServiceServiceInterface {
	mock := &MockServiceServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceServiceInterface) EXPECT() *MockServiceServiceInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockServiceServiceInterface) Create(ctx context.Context, orgID *appioid.ID, svcReq models.ServiceRequest, customPrefix string) (*appioid.ID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, orgID, svcReq, customPrefix)
	ret0, _ := ret[0].(*appioid.ID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockServiceServiceInterfaceMockRecorder) Create(ctx, orgID, svcReq, customPrefix any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockServiceServiceInterface)(nil).Create), ctx, orgID, svcReq, customPrefix)
}

// FindByID mocks base method.
func (m *MockServiceServiceInterface) FindByID(ctx context.Context, svcID *appioid.ID) (*models.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, svcID)
	ret0, _ := ret[0].(*models.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockServiceServiceInterfaceMockRecorder) FindByID(ctx, svcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockServiceServiceInterface)(nil).FindByID), ctx, svcID)
}

// FindByIDWithWidgetConfigs mocks base method.
func (m *MockServiceServiceInterface) FindByIDWithWidgetConfigs(ctx context.Context, platform models.Platform, svcID *appioid.ID) (*models.ServiceWithWidgetConfigs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByIDWithWidgetConfigs", ctx, platform, svcID)
	ret0, _ := ret[0].(*models.ServiceWithWidgetConfigs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIDWithWidgetConfigs indicates an expected call of FindByIDWithWidgetConfigs.
func (mr *MockServiceServiceInterfaceMockRecorder) FindByIDWithWidgetConfigs(ctx, platform, svcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIDWithWidgetConfigs", reflect.TypeOf((*MockServiceServiceInterface)(nil).FindByIDWithWidgetConfigs), ctx, platform, svcID)
}

// FindByIDWithWidgets mocks base method.
func (m *MockServiceServiceInterface) FindByIDWithWidgets(ctx context.Context, svcID *appioid.ID) (*models.ServiceWithWidgets, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByIDWithWidgets", ctx, svcID)
	ret0, _ := ret[0].(*models.ServiceWithWidgets)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIDWithWidgets indicates an expected call of FindByIDWithWidgets.
func (mr *MockServiceServiceInterfaceMockRecorder) FindByIDWithWidgets(ctx, svcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIDWithWidgets", reflect.TypeOf((*MockServiceServiceInterface)(nil).FindByIDWithWidgets), ctx, svcID)
}

// ListByDeviceWithWidgetConfigs mocks base method.
func (m *MockServiceServiceInterface) ListByDeviceWithWidgetConfigs(ctx context.Context, platform models.Platform, dvcID *appioid.ID) ([]models.ServiceWithWidgetConfigs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByDeviceWithWidgetConfigs", ctx, platform, dvcID)
	ret0, _ := ret[0].([]models.ServiceWithWidgetConfigs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByDeviceWithWidgetConfigs indicates an expected call of ListByDeviceWithWidgetConfigs.
func (mr *MockServiceServiceInterfaceMockRecorder) ListByDeviceWithWidgetConfigs(ctx, platform, dvcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByDeviceWithWidgetConfigs", reflect.TypeOf((*MockServiceServiceInterface)(nil).ListByDeviceWithWidgetConfigs), ctx, platform, dvcID)
}

// ListByOrganization mocks base method.
func (m *MockServiceServiceInterface) ListByOrganization(ctx context.Context, orgID *appioid.ID) ([]models.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByOrganization", ctx, orgID)
	ret0, _ := ret[0].([]models.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByOrganization indicates an expected call of ListByOrganization.
func (mr *MockServiceServiceInterfaceMockRecorder) ListByOrganization(ctx, orgID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByOrganization", reflect.TypeOf((*MockServiceServiceInterface)(nil).ListByOrganization), ctx, orgID)
}

// Update mocks base method.
func (m *MockServiceServiceInterface) Update(ctx context.Context, svcID *appioid.ID, svcReq models.ServiceRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, svcID, svcReq)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockServiceServiceInterfaceMockRecorder) Update(ctx, svcID, svcReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockServiceServiceInterface)(nil).Update), ctx, svcID, svcReq)
}
